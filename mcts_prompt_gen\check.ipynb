{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["81187\n"]}], "source": ["import dill\n", "# ps = dill.load(open('promptlabels_truly_fixed.pkl', 'rb'))\n", "ps = dill.load(open('production_dataset.pkl', 'rb'))\n", "print(len(ps))"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["3\n", "F:\\SD-webui\\ComfyUI\\output\\ComfyUI_00002_.png\n", "[('mignon', 0.9), ('fukuro_daizi', 0.9), ('2sham', 0.95), ('ne<PERSON><PERSON><PERSON>', 0.95), ('too<PERSON><PERSON>_asagi', 1.0), ('newest', 1.0), ('hazuki_natsu', 1.0), ('sho_sho_lwlw', 1.0), ('oh<PERSON><PERSON><PERSON>i', 1.0), ('tianliang_duohe_fangdongye', 1.0), ('pumpkinspicelatte', 1.0), ('1girl', 1.0), ('animal_ears', 1.0), ('grey_hair', 1.0), ('sleeves_past_wrists', 1.0), ('apron', 1.0), ('maid_headdress', 1.0), ('cat_ears', 1.0), ('long_hair', 1.0), ('white_background', 1.0), ('cat_girl', 1.0), ('hair_ornament', 1.0), ('sleeves_past_fingers', 1.0), ('blue_eyes', 1.0), ('cetacean_tail', 1.0), ('smile', 1.0), ('bangs', 1.0), ('white_apron', 1.0), ('fang', 1.0), ('long_sleeves', 1.0), (':d', 1.0), ('frilled_apron', 1.0), ('striped', 1.0), ('heart', 1.0), ('maid', 1.0), ('simple_background', 1.0), ('open_mouth', 1.0), ('solo', 1.0), ('shirt', 1.0), ('frills', 1.0), ('virtual_youtuber', 1.0), ('very_long_hair', 1.0), ('bow', 1.0), ('looking_at_viewer', 1.0), ('blue_dress', 1.0), ('blue_bow', 1.0)]\n", "0.0\n"]}], "source": ["print(len(ps[0]))\n", "for p in ps[0]:\n", "    print(p)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5655\n", "1513\n", "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00099_.png\n", "[('blush', 0.4), ('hiten', 0.1), ('sy4', 0.4), ('imigimuru', 0.4), ('condom_wrapper', 1.3), ('upper_body', 0.7), ('hood_up', 1.2), ('hood', 1.1), ('embarrassed', 0.8), ('ahoge', 0.6), ('23', 0.9), ('2', 0.9), ('artist:sho_sho_lwlw', 0.9), ('artist:alpha', 0.9), ('1girl', 1.0), ('yanami', 1.0), ('blue_hair', 1.0), ('medium_hair', 1.0), ('looking_at_viewer', 1.0), ('cross_necklace', 1.0), ('yellow_raincoat', 1.0), ('solo', 1.0), ('portrait', 1.0), ('holding', 1.0), ('artist:ciloranko', 1.0), ('half-closed_eyes', 1.0), ('closed_mouth', 1.0), ('indoors', 1.0), ('bedroom', 1.0), ('bed', 1.0), ('pillow', 1.0), ('masterpiece', 1.0), ('best_quality', 1.0), ('absurdres', 1.0), ('amazing_quality', 1.0), ('very_aesthetic', 1.0), ('highly_detailed', 1.0), ('high_res', 1.0), ('finely_detailed', 1.0), ('--seed', 1.0), ('<lora:luce2_Noob75XL>', 0.6)]\n"]}], "source": ["# find ids of ps that has \"artist:ciloranko\" or \"ciloranko\"\n", "ids = [i for i, p in enumerate(ps) if any(\"ciloranko\" in t[0] for t in p[1])]\n", "print(len(ids))\n", "idx = ids[0]\n", "print(idx)\n", "print(ps[idx][0])\n", "print(ps[idx][1])"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "1513\n", "F:\\SD-webui\\ComfyUI\\output\\2024-12-17\\ComfyUI_00099_.png\n", "[('blush', 0.4), ('hiten', 0.1), ('sy4', 0.4), ('imigimuru', 0.4), ('condom_wrapper', 1.3), ('upper_body', 0.7), ('hood_up', 1.2), ('hood', 1.1), ('embarrassed', 0.8), ('ahoge', 0.6), ('23', 0.9), ('2', 0.9), ('artist:sho_sho_lwlw', 0.9), ('artist:alpha', 0.9), ('1girl', 1.0), ('yanami', 1.0), ('blue_hair', 1.0), ('medium_hair', 1.0), ('looking_at_viewer', 1.0), ('cross_necklace', 1.0), ('yellow_raincoat', 1.0), ('solo', 1.0), ('portrait', 1.0), ('holding', 1.0), ('artist:ciloranko', 1.0), ('half-closed_eyes', 1.0), ('closed_mouth', 1.0), ('indoors', 1.0), ('bedroom', 1.0), ('bed', 1.0), ('pillow', 1.0), ('masterpiece', 1.0), ('best_quality', 1.0), ('absurdres', 1.0), ('amazing_quality', 1.0), ('very_aesthetic', 1.0), ('highly_detailed', 1.0), ('high_res', 1.0), ('finely_detailed', 1.0), ('--seed', 1.0), ('<lora:luce2_Noob75XL>', 0.6)]\n"]}], "source": ["# find filename that contains \"ComfyUI_00212_.png\"\n", "# idx = [i for i, p in enumerate(ps) if \"2025-06-22\\\\ComfyUI_00049_.png\" in p[0]]\n", "idx = [i for i, p in enumerate(ps) if \"2024-12-17\\\\ComfyUI_00099_.png\" in p[0]]\n", "print(len(idx))\n", "idx = idx[0]\n", "print(idx)\n", "print(ps[idx][0])\n", "print(ps[idx][1])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.7"}}, "nbformat": 4, "nbformat_minor": 2}